<?php $__env->startSection('title', 'Tableau de Bord - Gestionnaire Ciment'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dashboard-header {
        background: var(--primary-gradient);
        border-radius: var(--border-radius);
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50px;
        right: -50px;
        width: 300px;
        height: 300px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    .dashboard-header::after {
        content: '';
        position: absolute;
        bottom: -30px;
        left: -30px;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
        animation: float 8s ease-in-out infinite reverse;
    }

    .dashboard-title {
        font-size: 2.8rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }

    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 2;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .stats-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
        height: 100%;
        cursor: pointer;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        transition: height 0.3s ease;
    }

    .stats-card.primary::before { background: var(--primary-gradient); }
    .stats-card.success::before { background: var(--success-gradient); }
    .stats-card.info::before { background: var(--info-gradient); }
    .stats-card.warning::before { background: var(--warning-gradient); }
    .stats-card.danger::before { background: var(--danger-gradient); }

    .stats-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--card-hover-shadow);
    }

    .stats-card:hover::before {
        height: 8px;
    }

    .stats-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-bottom: 1.5rem;
        position: relative;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .stats-icon::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: inherit;
        opacity: 0.3;
        transform: scale(1.2);
        animation: pulse 2s infinite;
    }

    .stats-icon.primary { background: var(--primary-gradient); }
    .stats-icon.success { background: var(--success-gradient); }
    .stats-icon.warning { background: var(--warning-gradient); }
    .stats-icon.info { background: var(--info-gradient); }
    .stats-icon.danger { background: var(--danger-gradient); }

    @keyframes pulse {
        0% { transform: scale(1.2); opacity: 0.3; }
        50% { transform: scale(1.4); opacity: 0.1; }
        100% { transform: scale(1.2); opacity: 0.3; }
    }

    .stats-number {
        font-size: 3rem;
        font-weight: 800;
        color: #2d3748;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-label {
        color: #718096;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .stats-change {
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }

    .stats-change.positive { color: #38a169; }
    .stats-change.negative { color: #e53e3e; }

    .dashboard-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        overflow: hidden;
        margin-bottom: 2rem;
        height: 100%;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    .dashboard-card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
        position: relative;
    }

    .dashboard-card-body {
        padding: 1.5rem;
    }

    .dashboard-card-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .quick-action-btn {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-decoration: none;
        color: #4a5568;
        transition: var(--transition);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        height: 100%;
    }

    .quick-action-btn:hover {
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-3px);
        box-shadow: var(--card-shadow);
        text-decoration: none;
    }

    .quick-action-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        background: #f7fafc;
        color: #4a5568;
        transition: var(--transition);
    }

    .quick-action-btn:hover .quick-action-icon {
        background: var(--primary-gradient);
        color: white;
        transform: scale(1.1);
    }

    .recent-activity-item {
        padding: 1rem;
        border-bottom: 1px solid #e2e8f0;
        transition: var(--transition);
    }

    .recent-activity-item:hover {
        background: #f7fafc;
    }

    .recent-activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        color: white;
        margin-right: 1rem;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 10px;
        background: #e2e8f0;
        overflow: hidden;
        position: relative;
    }

    .progress-bar-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 1s ease-in-out;
        position: relative;
        overflow: hidden;
    }

    .progress-bar-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-image: linear-gradient(
            -45deg,
            rgba(255, 255, 255, .2) 25%,
            transparent 25%,
            transparent 50%,
            rgba(255, 255, 255, .2) 50%,
            rgba(255, 255, 255, .2) 75%,
            transparent 75%,
            transparent
        );
        background-size: 50px 50px;
        animation: move 2s linear infinite;
    }

    @keyframes move {
        0% { background-position: 0 0; }
        100% { background-position: 50px 50px; }
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-validated {
        background: linear-gradient(135deg, #11998e, #38ef7d);
        color: white;
    }

    .product-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #667eea;
    }

    .btn-modern {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: var(--transition);
        border: none;
    }

    .btn-primary-modern {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-info-modern {
        background: var(--info-gradient);
        color: white;
    }

    .btn-info-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .search-box {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .amount-highlight {
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .stats-card {
            margin-bottom: 1rem;
        }

        .supply-card-header,
        .supply-card-body {
            padding: 1rem;
        }

        .stats-number {
            font-size: 2rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid fade-in">
    <!-- En-tête du tableau de bord -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="dashboard-title">
                    <i class="fas fa-tachometer-alt me-3"></i>Tableau de Bord
                </h1>
                <p class="dashboard-subtitle">
                    Bienvenue <?php echo e(auth()->user()->first_name); ?> ! Voici un aperçu de vos activités
                    <br><small><i class="fas fa-calendar-alt me-2"></i><?php echo e(now()->locale('fr')->isoFormat('dddd D MMMM YYYY')); ?> | <span id="currentTime"><?php echo e(now()->format('H:i')); ?></span></small>
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex justify-content-md-end align-items-center gap-2">
                    <button class="btn btn-light btn-modern" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-outline-light btn-modern dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-2"></i>Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo e(route('cement-manager.sales.create')); ?>"><i class="fas fa-plus me-2"></i>Nouvelle Vente</a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('cement-manager.sales.export.excel')); ?>"><i class="fas fa-file-excel me-2"></i>Export Excel</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('cement-manager.profile.show')); ?>"><i class="fas fa-user me-2"></i>Mon Profil</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Principales -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card primary" onclick="window.location.href='<?php echo e(route('cement-manager.sales.index')); ?>'">
                <div class="stats-icon primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['total_sales'] ?? 0); ?></div>
                <div class="stats-label">Total Ventes</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% ce mois</span>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success" onclick="window.location.href='<?php echo e(route('cement-manager.sales.index')); ?>'">
                <div class="stats-icon success">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-number"><?php echo e(number_format($dashboardStats['total_revenue'] ?? 0, 0)); ?></div>
                <div class="stats-label">Chiffre d'Affaires (FCFA)</div>
                <div class="stats-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8% ce mois</span>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card warning" onclick="window.location.href='<?php echo e(route('cement-manager.sales.index')); ?>'">
                <div class="stats-icon warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['sales_pending'] ?? 0); ?></div>
                <div class="stats-label">Ventes en Attente</div>
                <div class="stats-change negative">
                    <i class="fas fa-arrow-down"></i>
                    <span>-5% ce mois</span>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card info" onclick="window.location.href='<?php echo e(route('cement-manager.trucks.index')); ?>'">
                <div class="stats-icon info">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['available_trucks'] ?? 0); ?></div>
                <div class="stats-label">Camions Disponibles</div>
                <div class="stats-change positive">
                    <i class="fas fa-check-circle"></i>
                    <span><?php echo e($dashboardStats['total_trucks'] ?? 0); ?> total</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Secondaires -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card info">
                <div class="stats-icon info">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['deliveries_in_progress'] ?? 0); ?></div>
                <div class="stats-label">Livraisons en Cours</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card success">
                <div class="stats-icon success">
                    <i class="fas fa-check-double"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['deliveries_completed'] ?? 0); ?></div>
                <div class="stats-label">Livraisons Terminées</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card primary">
                <div class="stats-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['available_drivers'] ?? 0); ?></div>
                <div class="stats-label">Chauffeurs Disponibles</div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card danger">
                <div class="stats-icon danger">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stats-number"><?php echo e($dashboardStats['total_supplies'] ?? 0); ?></div>
                <div class="stats-label">Approvisionnements</div>
            </div>
        </div>
    </div>

    <!-- Actions Rapides -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5 class="dashboard-card-title">
                        <i class="fas fa-bolt text-warning"></i>
                        Actions Rapides
                    </h5>
                </div>
                <div class="dashboard-card-body">
                    <div class="row justify-content-center">
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-3">
                            <a href="<?php echo e(route('cement-manager.sales.create')); ?>" class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <span class="fw-bold">Nouvelle Vente</span>
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-3">
                            <a href="<?php echo e(route('cement-manager.sales.index')); ?>" class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <span class="fw-bold">Voir Ventes</span>
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-3">
                            <a href="<?php echo e(route('cement-manager.supplies.index')); ?>" class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <span class="fw-bold">Approvisionnements</span>
                            </a>
                        </div>
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 mb-3">
                            <a href="<?php echo e(route('cement-manager.sales.export.excel')); ?>" class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="fas fa-download"></i>
                                </div>
                                <span class="fw-bold">Exporter</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu Principal du Tableau de Bord -->
    <div class="row">
        <!-- Graphique des Ventes -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5 class="dashboard-card-title">
                        <i class="fas fa-chart-line text-primary"></i>
                        Évolution des Ventes
                    </h5>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statut des Livraisons -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5 class="dashboard-card-title">
                        <i class="fas fa-shipping-fast text-info"></i>
                        Statut des Livraisons
                    </h5>
                </div>
                <div class="dashboard-card-body">
                    <div class="chart-container">
                        <canvas id="deliveryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Ventes Récentes -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header d-flex justify-content-between align-items-center">
                    <h5 class="dashboard-card-title">
                        <i class="fas fa-clock text-warning"></i>
                        Ventes Récentes
                    </h5>
                    <a href="<?php echo e(route('cement-manager.sales.index')); ?>" class="btn btn-sm btn-outline-primary">
                        Voir tout <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
                <div class="dashboard-card-body p-0">
                    <?php if(isset($dashboardStats['recent_sales']) && $dashboardStats['recent_sales']->count() > 0): ?>
                        <?php $__currentLoopData = $dashboardStats['recent_sales']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="recent-activity-item">
                                <div class="d-flex align-items-center">
                                    <div class="activity-icon bg-primary">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1 fw-bold"><?php echo e($sale->customer_name ?? 'Client non défini'); ?></h6>
                                                <p class="text-muted mb-0 small">
                                                    <i class="fas fa-map-marker-alt me-1"></i><?php echo e($sale->city->name ?? 'Ville non définie'); ?>

                                                    <span class="ms-2">
                                                        <i class="fas fa-weight me-1"></i><?php echo e(number_format($sale->quantity, 1)); ?>T
                                                    </span>
                                                </p>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold text-success"><?php echo e(number_format($sale->total_amount, 0)); ?> FCFA</div>
                                                <small class="text-muted"><?php echo e($sale->created_at->diffForHumans()); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">Aucune vente récente</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Performances et Objectifs -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5 class="dashboard-card-title">
                        <i class="fas fa-target text-success"></i>
                        Performances du Mois
                    </h5>
                </div>
                <div class="dashboard-card-body">
                    <!-- Objectif Ventes -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Objectif Ventes</span>
                            <span class="text-success fw-bold">75%</span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-bar-fill bg-success" style="width: 75%;"></div>
                        </div>
                        <small class="text-muted"><?php echo e($dashboardStats['total_sales'] ?? 0); ?> / 100 ventes</small>
                    </div>

                    <!-- Objectif Chiffre d'Affaires -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Objectif CA</span>
                            <span class="text-warning fw-bold">60%</span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-bar-fill bg-warning" style="width: 60%;"></div>
                        </div>
                        <small class="text-muted"><?php echo e(number_format($dashboardStats['total_revenue'] ?? 0, 0)); ?> / 50M FCFA</small>
                    </div>

                    <!-- Taux de Livraison -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Taux de Livraison</span>
                            <span class="text-info fw-bold">85%</span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-bar-fill bg-info" style="width: 85%;"></div>
                        </div>
                        <small class="text-muted"><?php echo e($dashboardStats['deliveries_completed'] ?? 0); ?> livraisons terminées</small>
                    </div>

                    <!-- Satisfaction Client -->
                    <div class="mb-0">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Satisfaction Client</span>
                            <span class="text-primary fw-bold">92%</span>
                        </div>
                        <div class="progress-bar-custom">
                            <div class="progress-bar-fill bg-primary" style="width: 92%;"></div>
                        </div>
                        <small class="text-muted">Basé sur les retours clients</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertes et Notifications -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <h5 class="dashboard-card-title">
                        <i class="fas fa-bell text-warning"></i>
                        Alertes et Notifications
                    </h5>
                </div>
                <div class="dashboard-card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="alert alert-warning border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-3 text-warning"></i>
                                    <div>
                                        <h6 class="mb-1"><?php echo e($dashboardStats['sales_pending'] ?? 0); ?> Ventes en Attente</h6>
                                        <small>Nécessitent votre attention</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="alert alert-info border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-truck me-3 text-info"></i>
                                    <div>
                                        <h6 class="mb-1"><?php echo e($dashboardStats['busy_trucks'] ?? 0); ?> Camions en Mission</h6>
                                        <small>Livraisons en cours</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="alert alert-success border-0 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle me-3 text-success"></i>
                                    <div>
                                        <h6 class="mb-1">Système Opérationnel</h6>
                                        <small>Tous les services fonctionnent</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mise à jour de l'heure en temps réel
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // Mettre à jour l'heure toutes les secondes
    setInterval(updateTime, 1000);
    updateTime(); // Appel initial

    // Fonction de rafraîchissement du tableau de bord
    window.refreshDashboard = function() {
        location.reload();
    };

    // Animation des cartes statistiques au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observer toutes les cartes
    document.querySelectorAll('.stats-card, .dashboard-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Graphique des ventes mensuelles
    const salesCtx = document.getElementById('salesChart');
    if (salesCtx) {
        new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
                datasets: [{
                    label: 'Ventes',
                    data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 38, 42, 45],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Graphique des livraisons (Doughnut)
    const deliveryCtx = document.getElementById('deliveryChart');
    if (deliveryCtx) {
        new Chart(deliveryCtx, {
            type: 'doughnut',
            data: {
                labels: ['En Attente', 'En Cours', 'Terminées'],
                datasets: [{
                    data: [
                        <?php echo e($dashboardStats['deliveries_pending'] ?? 5); ?>,
                        <?php echo e($dashboardStats['deliveries_in_progress'] ?? 8); ?>,
                        <?php echo e($dashboardStats['deliveries_completed'] ?? 25); ?>

                    ],
                    backgroundColor: ['#ffc107', '#17a2b8', '#28a745'],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    // Animation des barres de progression
    setTimeout(() => {
        document.querySelectorAll('.progress-bar-fill').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    }, 500);

    // Animation des nombres dans les statistiques
    function animateNumbers() {
        document.querySelectorAll('.stats-number').forEach(element => {
            const finalNumber = parseInt(element.textContent.replace(/\D/g, ''));
            if (finalNumber > 0) {
                const duration = 2000;
                const increment = finalNumber / (duration / 16);
                let currentNumber = 0;

                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        element.textContent = finalNumber.toLocaleString('fr-FR');
                        clearInterval(timer);
                    } else {
                        element.textContent = Math.floor(currentNumber).toLocaleString('fr-FR');
                    }
                }, 16);
            }
        });
    }

    // Démarrer l'animation des nombres après un court délai
    setTimeout(animateNumbers, 500);

    console.log('Tableau de bord Cement Manager initialisé avec succès! 🚀');
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.cement_manager', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cement-manager/dashboard.blade.php ENDPATH**/ ?>