@extends('layouts.cement_manager')

@section('title', 'Reç<PERSON> de Vente #' . $sale->id)

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
    /* Variables */
    :root {
        --primary-color: #1E88E5;
        --primary-light: #BBDEFB;
        --primary-dark: #0D47A1;
        --secondary-color: #4CAF50;
        --secondary-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --text-light: #667085;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --gradient-blue: linear-gradient(135deg, #1E88E5, #0D47A1);
        --box-shadow: 0 8px 20px rgba(13, 71, 161, 0.08);
    }

    /* Styles généraux pour le format A5 */
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.5;
        color: var(--text-color);
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }

    /* Format A5 : 148mm x 210mm */
    .receipt-page {
        width: 148mm;
        min-height: 210mm;
        margin: 0 auto 2rem;
        background: white;
        position: relative;
        padding: 0;
        box-shadow: var(--box-shadow);
        overflow: hidden;
        border-radius: 8px;
    }

    .receipt-container {
        width: 100%;
        height: 100%;
        background-color: #fff;
        position: relative;
        overflow: hidden;
        max-width: 148mm;
        margin: 0 auto;
    }

    /* En-tête du reçu avec dégradé - hauteur fortement réduite */
    .receipt-header {
        background: var(--gradient-blue);
        color: white;
        padding: 0.3rem 0.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    /* Logo container avec effet de brillance */
    .receipt-logo-container {
        background-color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.5rem;
        padding: 5px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
        z-index: 10;
    }

    .receipt-logo {
        max-width: 40px;
        max-height: 40px;
        z-index: 2;
    }

    /* Titre du reçu avec effet de texte */
    .receipt-title {
        font-size: 1.2rem;
        font-weight: 800;
        margin-bottom: 0.2rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    /* Badge pour le numéro de reçu */
    .receipt-number {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0.2rem;
        font-weight: 600;
    }

    /* Date du reçu avec icône - format compact */
    .receipt-date {
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.3rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .receipt-date i {
        font-size: 0.7rem;
    }

    /* Corps du reçu avec design moderne - espacement minimal */
    .receipt-body {
        padding: 0.75rem 0.75rem;
        position: relative;
        background-color: #FAFBFF;
        background-image: none;
    }

    /* Conteneur pour les informations client et vente */
    .receipt-info-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 0.3rem;
        margin-bottom: 0.3rem;
    }

    .receipt-info-column {
        width: 49%;
    }

    /* Sections avec style de carte moderne - espacement minimal */
    .receipt-section {
        margin-bottom: 0.5rem;
        background-color: white;
        border-radius: 6px;
        padding: 0.5rem;
        box-shadow: 0 2px 6px rgba(13, 71, 161, 0.03);
        border: 1px solid rgba(187, 222, 251, 0.2);
        overflow: hidden;
        position: relative;
    }

    /* Désactivation de l'effet hover pour l'impression */
    @media screen {
        .receipt-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 71, 161, 0.08);
        }
    }

    /* Titre de section avec accent color - espacement réduit */
    .receipt-section-title {
        font-size: 0.9rem;
        color: var(--primary-dark);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        padding-bottom: 0.5rem;
        font-weight: 600;
        border-bottom: 1px dashed var(--border-color);
    }

    .receipt-section-title i {
        margin-right: 0.4rem;
        color: var(--primary-color);
        font-size: 0.9rem;
    }

    .receipt-section-title::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 50px;
        height: 3px;
        background: var(--gradient-blue);
        border-radius: 10px;
    }

    /* Icônes de section avec style moderne */
    .receipt-section-title i {
        margin-right: 0.75rem;
        background: var(--primary-light);
        color: var(--primary-dark);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
    }

    /* Lignes d'information avec style moderne - format compact */
    .info-row {
        display: flex;
        margin-bottom: 0.3rem;
        align-items: flex-start;
    }

    .info-row:last-child {
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #0D47A1;
        min-width: 100px;
        font-size: 0.8rem;
    }

    .info-value {
        flex: 1;
        font-size: 0.8rem;
        color: #333;
        min-width: 60px;
        display: inline-block;
    }

    /* Tableau des produits avec design moderne - espacement réduit */
    .receipt-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 0.75rem;
        font-size: 0.75rem;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(13, 71, 161, 0.05);
    }

    /* En-tête du tableau avec dégradé */
    .receipt-table th {
        background: var(--gradient-blue);
        color: white;
        padding: 0.5rem 0.4rem;
        text-align: left;
        font-weight: 600;
        position: relative;
        text-transform: uppercase;
        font-size: 0.7rem;
        letter-spacing: 0.3px;
    }

    .receipt-table th:first-child {
        border-top-left-radius: 8px;
        padding-left: 0.75rem;
    }

    .receipt-table th:last-child {
        border-top-right-radius: 8px;
        padding-right: 0.75rem;
    }

    /* Cellules du tableau avec espacement réduit */
    .receipt-table td {
        padding: 0.5rem 0.4rem;
        border-bottom: 1px solid var(--border-color);
        background-color: white;
    }

    .receipt-table td:first-child {
        padding-left: 0.75rem;
    }

    .receipt-table td:last-child {
        padding-right: 0.75rem;
        font-weight: 700;
    }

    /* Effet de survol uniquement sur écran */
    @media screen {
        .receipt-table tr:hover td {
            background-color: rgba(187, 222, 251, 0.1);
        }
    }

    .receipt-table tr:last-child td {
        border-bottom: none;
    }

    .receipt-table tr:last-child td:first-child {
        border-bottom-left-radius: 8px;
    }

    .receipt-table tr:last-child td:last-child {
        border-bottom-right-radius: 8px;
    }

    /* Résumé des paiements avec style moderne */
    .payment-summary {
        background: white;
        color: var(--text-color);
        padding: 0;
        border-radius: 12px;
        font-size: 0.9rem;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(13, 71, 161, 0.06);
        border: 1px solid rgba(187, 222, 251, 0.3);
        position: relative;
    }

    /* Barre d'accent en haut du résumé */
    .payment-summary::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-blue);
    }

    /* Titre du résumé des paiements */
    .payment-summary-title {
        padding: 1rem 1.25rem;
        font-weight: 600;
        color: var(--primary-dark);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        background-color: rgba(187, 222, 251, 0.1);
    }

    .payment-summary-title i {
        margin-right: 0.75rem;
        background: var(--primary-light);
        color: var(--primary-dark);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Contenu du résumé des paiements */
    .payment-summary-content {
        padding: 1.25rem;
    }

    /* Lignes du résumé des paiements */
    .payment-summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px dashed rgba(187, 222, 251, 0.5);
        font-weight: 500;
    }

    .payment-summary-row:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        padding-top: 0.75rem;
        border-bottom: none;
        border-top: 1px solid rgba(13, 71, 161, 0.1);
        font-weight: 700;
        font-size: 1.1rem;
    }

    /* Valeurs dans le résumé des paiements */
    .payment-summary-value {
        font-weight: 600;
    }

    .payment-summary-row:last-child .payment-summary-value {
        color: var(--primary-dark);
    }


    /* Badge pour le mode de paiement avec style moderne */
    .payment-method-badge {
        display: inline-flex;
        align-items: center;
        background: var(--gradient-blue);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 30px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(13, 71, 161, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .payment-method-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 71, 161, 0.3);
    }

    .payment-method-badge i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    /* Badges de statut avec couleurs distinctes */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.75rem;
        border-radius: 30px;
        font-size: 0.8rem;
        font-weight: 600;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .status-badge:hover {
        transform: translateY(-2px);
    }

    .status-badge.pending {
        background: linear-gradient(to right, #FF9800, #F57C00);
        color: white;
    }

    .status-badge.partial {
        background: linear-gradient(to right, #03A9F4, #0288D1);
        color: white;
    }

    .status-badge.completed {
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        color: white;
    }

    .status-badge i {
        margin-right: 0.4rem;
        font-size: 0.8rem;
    }

    /* Section des signatures avec style moderne - format compact */
    .receipt-signatures {
        display: flex;
        justify-content: space-between;
        margin-top: 0.4rem;
        margin-bottom: 0.4rem;
    }

    .signature-box {
        text-align: center;
        width: 30%;
        position: relative;
    }

    .signature-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--gradient-blue);
    }

    .signature-line {
        border-bottom: 1px dashed var(--primary-light);
        margin-bottom: 0.2rem;
        height: 10px;
        position: relative;
    }

    .signature-line::before {
        content: '✒';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.9rem;
        opacity: 0.2;
    }

    .signature-title {
        font-size: 0.7rem;
        color: var(--primary-dark);
        font-weight: 600;
        margin-top: 0;
        margin-bottom: 0;
    }

    /* Code QR avec style minimal */
    .receipt-qr {
        position: absolute;
        right: 0.75rem;
        bottom: 0.75rem;
        text-align: center;
        opacity: 1;
    }

    @media screen {
        .receipt-qr:hover {
            transform: scale(1.05);
            opacity: 1;
        }
    }

    .receipt-qr img {
        max-width: 40px;
        border-radius: 3px;
        padding: 1px;
        background-color: white;
        box-shadow: 0 1px 2px rgba(13, 71, 161, 0.1);
        border: 1px solid rgba(187, 222, 251, 0.3);
    }

    .receipt-qr-text {
        font-size: 0.5rem;
        color: var(--primary-dark);
        margin-top: 0.1rem;
        font-weight: 600;
    }


    /* Section des termes et conditions - format minimal */
    .terms-conditions {
        font-size: 0.55rem;
        color: var(--text-light);
        text-align: center;
        margin-top: 0.3rem;
        padding: 0.2rem;
        border-top: 1px dashed rgba(187, 222, 251, 0.3);
        line-height: 1.1;
    }

    .terms-conditions-title {
        font-weight: 600;
        margin-bottom: 0.1rem;
        color: var(--primary-dark);
        font-size: 0.6rem;
    }

    /* Styles pour le cachet d'entreprise - taille réduite */
    .company-stamp {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 25px;
        margin-bottom: 0.5rem;
    }

    .stamp-circle {
        width: 50px;
        height: 50px;
        border: 1px dashed var(--primary-dark);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transform: rotate(-15deg);
        position: relative;
        background-color: rgba(13, 71, 161, 0.05);
    }

    .stamp-circle::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 42px;
        height: 42px;
        border: 1px solid var(--primary-light);
        border-radius: 50%;
        opacity: 0.5;
    }

    .stamp-text {
        font-weight: 800;
        color: var(--primary-dark);
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }

    .stamp-subtext {
        font-size: 0.5rem;
        color: var(--primary-dark);
        font-weight: 600;
    }

    /* Note d'information avec icône */
    .receipt-note {
        display: flex;
        align-items: flex-start;
        background-color: rgba(187, 222, 251, 0.1);
        border: 1px solid rgba(187, 222, 251, 0.3);
        border-radius: 8px;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
        font-size: 0.7rem;
        color: var(--text-color);
    }

    .receipt-note i {
        color: var(--primary-color);
        margin-right: 0.5rem;
        margin-top: 0.1rem;
        flex-shrink: 0;
    }

    /* Styles d'impression */
    @media print {
        /* Masquer la sidebar et la navbar lors de l'impression */
        .sidebar, .navbar, .breadcrumb, .no-print {
            display: none !important;
        }

        /* Ajuster le contenu pour l'impression */
        .main-content {
            margin-left: 0 !important;
            padding: 0 !important;
        }

        body * {
            visibility: hidden;
        }
        .receipt-page, .receipt-page * {
            visibility: visible;
        }
        .receipt-page {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            margin: 0;
            box-shadow: none;
            border-radius: 0;
        }
    }

    /* Styles pour l'affichage avec sidebar */
    .receipt-wrapper {
        max-width: 800px;
        margin: 0 auto;
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <!-- Header avec breadcrumb et actions -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-receipt text-primary me-2"></i>
                Reçu de Vente #{{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="{{ route('cement-manager.dashboard') }}">
                            <i class="fas fa-home"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ route('cement-manager.sales.index') }}">
                            <i class="fas fa-history"></i> Historique des ventes
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">Reçu</li>
                </ol>
            </nav>
        </div>
        <div class="no-print">
            <a href="{{ route('cement-manager.sales.index') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> Imprimer au format A5
            </button>
        </div>
    </div>

    <!-- Wrapper pour centrer le reçu -->
    <div class="receipt-wrapper">
        <!-- Début de la zone imprimable au format A5 -->
        <div class="receipt-page">
        <div class="receipt-container">
            <div class="receipt-header">
                <div class="receipt-logo-container">
                    <img src="{{ asset('assets/images/logo_gradis.png') }}" alt="Logo" class="receipt-logo">
                </div>
                <h1 class="receipt-title">REÇU DE VENTE</h1>
                <p class="receipt-number"># {{ 'VEN-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</p>
                <p class="receipt-date"><i class="fas fa-calendar-alt"></i> {{ $sale->created_at->format('d/m/Y H:i') }}</p>
            </div>

            <div class="receipt-body">
                <div class="receipt-info-container">
                    <div class="receipt-info-column">
                        <div class="receipt-section">
                            <h5 class="receipt-section-title"><i class="fas fa-user"></i> Informations client</h5>
                            <div class="info-row">
                                <div class="info-label">Nom:</div>
                                <div class="info-value">{{ $sale->customer_name ?? 'Non spécifié' }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Téléphone:</div>
                                <div class="info-value">{{ $sale->customer_phone ?? 'Non spécifié' }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Adresse:</div>
                                <div class="info-value">{{ $sale->customer_address ?? 'Non spécifiée' }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="receipt-info-column">
                        <div class="receipt-section">
                            <h5 class="receipt-section-title"><i class="fas fa-shopping-cart"></i> Détails de la vente</h5>
                            <div class="info-row">
                                <div class="info-label">Vendeur:</div>
                                <div class="info-value">{{ $sale->createdBy->name ?? 'Non spécifié' }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Ville:</div>
                                <div class="info-value">{{ $sale->city->name ?? 'Non spécifiée' }}</div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Date:</div>
                                <div class="info-value">{{ $sale->created_at->format('d/m/Y H:i') }}</div>
                            </div>
                            @if($sale->driver)
                            <div class="info-row">
                                <div class="info-label">Chauffeur:</div>
                                <div class="info-value">{{ $sale->driver->name }}</div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Tableau des produits -->
                <div class="receipt-section">
                    <h5 class="receipt-section-title"><i class="fas fa-box"></i> Détails des produits</h5>
                    @if($sale->supply && $sale->supply->details && $sale->supply->details->count() > 0)
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th>Produit</th>
                                    <th>Quantité</th>
                                    <th>Prix Unit.</th>
                                    @if($sale->discount_per_ton > 0)
                                    <th>Remise</th>
                                    @endif
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $subtotal = $sale->quantity * $sale->unit_price;
                                    $discount_total = $sale->discount_per_ton * $sale->quantity;
                                    $line_total = $subtotal - $discount_total;
                                @endphp
                                <tr>
                                    <td>
                                        {{ $sale->supply->details->first()->product->name ?? 'Produit non spécifié' }}
                                        @if($sale->supply->reference)
                                        <br><small>Réf: {{ $sale->supply->reference }}</small>
                                        @endif
                                    </td>
                                    <td>{{ number_format($sale->quantity, 2, ',', ' ') }} T</td>
                                    <td>{{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA</td>
                                    @if($sale->discount_per_ton > 0)
                                    <td>-{{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA</td>
                                    @endif
                                    <td>{{ number_format($line_total, 0, ',', ' ') }} FCFA</td>
                                </tr>
                            </tbody>
                        </table>
                    @else
                        <p class="text-center text-muted">Aucun produit trouvé</p>
                    @endif
                </div>

                <div class="receipt-info-container">
                    <div class="receipt-info-column">
                        <!-- Espace vide pour équilibrer -->
                    </div>
                    <div class="receipt-info-column">
                        <div class="payment-summary">
                            <div class="payment-summary-title">
                                <i class="fas fa-calculator"></i> Résumé financier
                            </div>
                            <div class="payment-summary-content">
                                <div class="payment-summary-row">
                                    <div>Montant total de la vente:</div>
                                    <div class="payment-summary-value">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                </div>
                                @if($sale->discount_per_ton > 0)
                                <div class="payment-summary-row">
                                    <div>Remise totale:</div>
                                    <div class="payment-summary-value">{{ number_format($sale->discount_per_ton * $sale->quantity, 0, ',', ' ') }} FCFA</div>
                                </div>
                                @endif
                                <div class="payment-summary-row">
                                    <div>Montant à payer:</div>
                                    <div class="payment-summary-value">{{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="receipt-signatures">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p class="signature-title">Signature du vendeur</p>
                    </div>
                    <div class="signature-box stamp">
                        <div class="company-stamp">
                            <div class="stamp-circle">
                                <div class="stamp-text">GRADIS</div>
                                <div class="stamp-subtext">OFFICIEL</div>
                            </div>
                        </div>
                        <p class="signature-title">Cachet de l'entreprise</p>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p class="signature-title">Signature du client</p>
                    </div>
                </div>

                <div class="receipt-note">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Info :</strong> Preuve officielle de vente. À conserver pour réclamations.
                    </div>
                </div>

                <div class="receipt-qr">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data={{ urlencode(route('cement-manager.sales.receipt', $sale->id)) }}" alt="QR Code">
                    <p class="receipt-qr-text">Scannez pour vérifier l'authenticité</p>
                </div>

                <div class="terms-conditions">
                    <p class="terms-conditions-title">Termes et Conditions</p>
                    <p>Valable avec cachet. Remboursement sous 7 jours. Réclamation avec reçu original uniquement.</p>
                </div>
            </div>

            <!-- Pied de page supprimé -->
        </div>
    </div>
    </div> <!-- Fin receipt-wrapper -->
</div> <!-- Fin container-fluid -->
@endsection
