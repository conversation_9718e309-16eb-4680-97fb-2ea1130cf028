@extends('layouts.cement_manager')

@section('title', 'Reçu de Vente #' . $sale->id)

@push('styles')
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    @media print {
        body * {
            visibility: hidden;
        }
        .receipt-container, .receipt-container * {
            visibility: visible;
        }
        .receipt-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
        .page-break {
            page-break-after: always;
        }
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', sans-serif;
    }

    .receipt-container {
        max-width: 148mm;
        margin: 0 auto;
        background: white;
        padding: 0;
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        border-radius: 20px;
        overflow: hidden;
        position: relative;
    }

    .receipt-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    }

    .receipt-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 30px 20px;
        position: relative;
        overflow: hidden;
    }

    .receipt-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(1deg); }
    }

    .company-logo {
        width: 90px;
        height: 90px;
        margin: 0 auto 15px;
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(10px);
        border: 3px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        font-weight: 800;
        position: relative;
        z-index: 2;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .receipt-title {
        font-size: 32px;
        font-weight: 800;
        color: white;
        margin: 15px 0 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: relative;
        z-index: 2;
        letter-spacing: 2px;
    }

    .receipt-subtitle {
        font-size: 14px;
        color: rgba(255,255,255,0.9);
        margin-bottom: 20px;
        font-weight: 400;
        position: relative;
        z-index: 2;
    }

    .receipt-number {
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        color: white;
        display: inline-block;
        margin-bottom: 10px;
        position: relative;
        z-index: 2;
        border: 1px solid rgba(255,255,255,0.3);
    }

    .receipt-date {
        font-size: 13px;
        color: rgba(255,255,255,0.8);
        position: relative;
        z-index: 2;
        font-weight: 500;
    }

    .receipt-body {
        padding: 30px 25px;
    }

    .receipt-section {
        margin: 25px 0;
        padding: 0;
        background: transparent;
        border-radius: 15px;
        position: relative;
        overflow: hidden;
    }

    .section-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #f0f0f0;
        position: relative;
        transition: all 0.3s ease;
    }

    .section-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .section-title {
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 15px;
        font-size: 16px;
        display: flex;
        align-items: center;
        position: relative;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 16px;
        color: white;
        font-weight: 600;
    }

    .product-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .customer-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .payment-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f7fafc;
        font-size: 14px;
    }

    .info-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 500;
        color: #718096;
        flex: 1;
    }

    .info-value {
        color: #2d3748;
        text-align: right;
        font-weight: 600;
        flex: 1;
    }

    .total-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 25px;
        margin: 30px 0;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .total-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
        background-size: 20px 20px;
        animation: sparkle 15s linear infinite;
    }

    @keyframes sparkle {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .total-breakdown {
        position: relative;
        z-index: 2;
    }

    .total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
        color: rgba(255,255,255,0.9);
    }

    .total-amount {
        font-size: 28px;
        font-weight: 800;
        color: white;
        text-align: center;
        margin-top: 15px;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-badge i {
        margin-right: 5px;
        font-size: 10px;
    }

    .status-completed {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
    }

    .status-pending {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
    }

    .status-cancelled {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
    }

    .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        padding-top: 30px;
        border-top: 2px dashed #e2e8f0;
    }

    .signature-box {
        text-align: center;
        width: 45%;
        position: relative;
    }

    .signature-line {
        border-top: 2px solid #4a5568;
        margin-top: 50px;
        padding-top: 8px;
        font-size: 12px;
        color: #718096;
        font-weight: 500;
        position: relative;
    }

    .signature-line::before {
        content: '✓';
        position: absolute;
        top: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 20px;
        background: #48bb78;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
    }

    .receipt-footer {
        background: #f7fafc;
        text-align: center;
        padding: 25px;
        margin-top: 30px;
        border-top: 1px solid #e2e8f0;
    }

    .footer-logo {
        font-size: 18px;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 8px;
    }

    .footer-text {
        font-size: 12px;
        color: #718096;
        line-height: 1.5;
        margin-bottom: 5px;
    }

    .footer-contact {
        font-size: 11px;
        color: #a0aec0;
        margin-top: 10px;
    }

    .qr-code {
        text-align: center;
        margin: 25px 0;
        padding: 20px;
        background: #f7fafc;
        border-radius: 15px;
        border: 2px dashed #cbd5e0;
    }

    .highlight-value {
        background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);
        padding: 4px 8px;
        border-radius: 8px;
        color: #c05621;
        font-weight: 600;
    }

    /* Styles pour le tableau des produits */
    .products-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }

    .products-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .products-table thead th {
        padding: 15px 12px;
        text-align: left;
        font-weight: 600;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
    }

    .products-table tbody tr {
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .products-table tbody tr:hover {
        background: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .products-table tbody tr:last-child {
        border-bottom: none;
    }

    .products-table tbody td {
        padding: 15px 12px;
        font-size: 14px;
        color: #2d3748;
        border: none;
        vertical-align: middle;
    }

    .product-name {
        font-weight: 600;
        color: #4a5568;
    }

    .product-reference {
        font-size: 12px;
        color: #718096;
        font-style: italic;
        margin-top: 3px;
    }

    .quantity-badge {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
        display: inline-block;
        text-align: center;
        min-width: 60px;
    }

    .price-cell {
        text-align: right;
        font-weight: 600;
        color: #2d3748;
    }

    .discount-cell {
        text-align: right;
        color: #e53e3e;
        font-weight: 600;
    }

    .total-cell {
        text-align: right;
        font-weight: 700;
        color: #667eea;
        font-size: 15px;
    }

    .table-summary {
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-top: 2px solid #e2e8f0;
    }

    .table-summary td {
        font-weight: 600;
        color: #4a5568;
        padding: 18px 12px !important;
    }

    .no-products {
        text-align: center;
        padding: 40px 20px;
        color: #718096;
        font-style: italic;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Boutons d'action -->
    <div class="row mb-4 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ route('cement-manager.sales.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fas fa-print me-1"></i> Imprimer
                    </button>
                    <a href="{{ route('cement-manager.sales.receipt-pdf', $sale) }}" class="btn btn-danger" target="_blank">
                        <i class="fas fa-file-pdf me-1"></i> PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Reçu de vente -->
    <div class="receipt-container">
        <!-- En-tête Premium -->
        <div class="receipt-header">
            <div class="company-logo">
                <i class="fas fa-industry"></i>
            </div>
            <h1 class="receipt-title">CIMTOGO</h1>
            <div class="receipt-subtitle">Ciment de Qualité Supérieure</div>
            <div class="receipt-number">REÇU #{{ str_pad($sale->id, 6, '0', STR_PAD_LEFT) }}</div>
            <div class="receipt-date">{{ $sale->created_at->format('d/m/Y à H:i') }}</div>
        </div>

        <div class="receipt-body">

            <!-- Tableau des produits -->
            <div class="receipt-section">
                <div class="section-card">
                    <div class="section-title">
                        <div class="section-icon product-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        Détails des Produits
                    </div>

                    @if($sale->supply && $sale->supply->details && $sale->supply->details->count() > 0)
                        <table class="products-table">
                            <thead>
                                <tr>
                                    <th style="width: 40%;">Produit</th>
                                    <th style="width: 15%; text-align: center;">Quantité</th>
                                    <th style="width: 15%; text-align: right;">Prix Unit.</th>
                                    @if($sale->discount_per_ton > 0)
                                    <th style="width: 15%; text-align: right;">Remise</th>
                                    @endif
                                    <th style="width: 15%; text-align: right;">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $subtotal = $sale->quantity * $sale->unit_price;
                                    $discount_total = $sale->discount_per_ton * $sale->quantity;
                                    $line_total = $subtotal - $discount_total;
                                @endphp
                                <tr>
                                    <td>
                                        <div class="product-name">
                                            {{ $sale->supply->details->first()->product->name ?? 'Produit non spécifié' }}
                                        </div>
                                        @if($sale->supply->reference)
                                        <div class="product-reference">
                                            <i class="fas fa-receipt me-1"></i>
                                            Réf: {{ $sale->supply->reference }}
                                        </div>
                                        @endif
                                    </td>
                                    <td style="text-align: center;">
                                        <span class="quantity-badge">
                                            {{ number_format($sale->quantity, 2, ',', ' ') }} T
                                        </span>
                                    </td>
                                    <td class="price-cell">
                                        {{ number_format($sale->unit_price, 0, ',', ' ') }} FCFA
                                    </td>
                                    @if($sale->discount_per_ton > 0)
                                    <td class="discount-cell">
                                        -{{ number_format($sale->discount_per_ton, 0, ',', ' ') }} FCFA
                                    </td>
                                    @endif
                                    <td class="total-cell">
                                        {{ number_format($line_total, 0, ',', ' ') }} FCFA
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot class="table-summary">
                                <tr>
                                    <td colspan="{{ $sale->discount_per_ton > 0 ? '4' : '3' }}" style="text-align: right; font-weight: 700;">
                                        <i class="fas fa-calculator me-2"></i>TOTAL GÉNÉRAL:
                                    </td>
                                    <td class="total-cell" style="font-size: 16px; color: #667eea;">
                                        {{ number_format($sale->total_amount, 0, ',', ' ') }} FCFA
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    @else
                        <div class="no-products">
                            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #ed8936; margin-bottom: 10px;"></i>
                            <p>Aucun produit trouvé pour cette vente</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Informations du client -->
            <div class="receipt-section">
                <div class="section-card">
                    <div class="section-title">
                        <div class="section-icon customer-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        Informations Client
                    </div>
                    <div class="info-row">
                        <span class="info-label">Nom</span>
                        <span class="info-value"><strong>{{ $sale->customer_name }}</strong></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Téléphone</span>
                        <span class="info-value">{{ $sale->customer_phone }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Adresse</span>
                        <span class="info-value">{{ $sale->customer_address }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Ville</span>
                        <span class="info-value">{{ $sale->city->name ?? 'N/A' }}</span>
                    </div>
                </div>
            </div>

            <!-- Informations de paiement -->
            <div class="receipt-section">
                <div class="section-card">
                    <div class="section-title">
                        <div class="section-icon payment-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        Détails du Paiement
                    </div>
                    <div class="info-row">
                        <span class="info-label">Mode de paiement</span>
                        <span class="info-value">
                            @if($sale->payment_method === 'cash')
                                <span class="status-badge status-completed">
                                    <i class="fas fa-money-bill-wave"></i> Comptant
                                </span>
                            @else
                                <span class="status-badge status-pending">
                                    <i class="fas fa-credit-card"></i> Crédit
                                </span>
                            @endif
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Statut</span>
                        <span class="info-value">
                            @if($sale->status === 'completed')
                                <span class="status-badge status-completed">
                                    <i class="fas fa-check-circle"></i> Terminé
                                </span>
                            @elseif($sale->status === 'pending')
                                <span class="status-badge status-pending">
                                    <i class="fas fa-clock"></i> En attente
                                </span>
                            @else
                                <span class="status-badge status-cancelled">
                                    <i class="fas fa-times-circle"></i> Annulé
                                </span>
                            @endif
                        </span>
                    </div>
                    @if($sale->trips)
                    <div class="info-row">
                        <span class="info-label">Nombre de voyages</span>
                        <span class="info-value"><strong>{{ $sale->trips }}</strong></span>
                    </div>
                    @endif
                </div>
            </div>



            <!-- Code QR (si disponible) -->
            @if($sale->qr_code)
            <div class="qr-code">
                <div style="display: inline-block; padding: 15px; background: white; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <img src="data:image/png;base64,{{ $sale->qr_code }}" alt="QR Code" style="width: 80px; height: 80px;">
                </div>
                <p style="margin-top: 10px; font-size: 11px; color: #718096;">Scannez pour plus d'informations</p>
            </div>
            @endif

            <!-- Signatures -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line">Signature du Client</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line">Signature du Vendeur</div>
                </div>
            </div>
        </div>

        <!-- Pied de page -->
        <div class="receipt-footer">
            <div class="footer-logo">CIMTOGO</div>
            <div class="footer-text"><strong>Ciment de qualité pour vos constructions</strong></div>
            <div class="footer-text">Merci pour votre confiance !</div>
            <div class="footer-text">Ce reçu fait foi de votre achat - Conservez-le précieusement</div>
            <div class="footer-contact">
                Généré le {{ now()->format('d/m/Y à H:i:s') }} par {{ auth()->user()->name ?? 'Système' }}<br>
                Contact: <EMAIL> | Tél: +228 XX XX XX XX
            </div>
        </div>
    </div>
</div>
@endsection
